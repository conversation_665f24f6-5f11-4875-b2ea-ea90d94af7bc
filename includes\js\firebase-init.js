// Validate Firebase configuration
const validateConfig = (config) => {
  if (!config) {
    console.error("Firebase configuration is missing");
    return false;
  }

  const requiredFields = [
    "apiKey",
    "authDomain",
    "projectId",
    "storageBucket",
    "messagingSenderId",
    "appId",
  ];
  const missingFields = requiredFields.filter((field) => !config[field]);

  if (missingFields.length > 0) {
    console.error(
      "Firebase configuration is missing required fields:",
      missingFields
    );
    return false;
  }

  return true;
};

// Initialize Firebase with error handling
let app, messaging;

// Create a promise that will resolve with the messaging instance
let messagingResolve;
const messagingPromise = new Promise((resolve) => {
  messagingResolve = resolve;
});

// Add a version query parameter to bust browser and proxy caches whenever the script is changed.
// Increase the value whenever you deploy a new service-worker build.
const Q_SW_VERSION = "20240625";

document.addEventListener("DOMContentLoaded", function () {
  try {
    // Load Firebase modules dynamically
    const loadFirebase = async () => {
      try {
        // Dynamic imports for Firebase modules
        const firebaseApp = await import(
          "https://www.gstatic.com/firebasejs/11.5.0/firebase-app.js"
        );
        const firebaseMessaging = await import(
          "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js"
        );

        const { initializeApp } = firebaseApp;
        const { getMessaging } = firebaseMessaging;

        if (!validateConfig(window.q_firebase_config)) {
          throw new Error("Invalid Firebase configuration");
        }

        app = initializeApp(window.q_firebase_config);
        messaging = getMessaging(app);
        if (window.location.hostname === "localhost") {
          console.log(
            "Firebase initialized successfully with project:",
            window.q_firebase_config.projectId
          );
        }

        // Register service worker
        if ("serviceWorker" in navigator) {
          const swUrl = `/firebase-messaging-sw.js?v=${Q_SW_VERSION}`;

          // Helper to show service worker registration issues (not offline-related)
          function showServiceWorkerIssue(message) {
            // Only show if there's a genuine service worker registration problem
            if (!document.getElementById("pwa-sw-issue-message")) {
              const msg = document.createElement("div");
              msg.id = "pwa-sw-issue-message";
              msg.style.position = "fixed";
              msg.style.bottom = "20px";
              msg.style.left = "50%";
              msg.style.transform = "translateX(-50%)";
              msg.style.background = "#f44336";
              msg.style.color = "#fff";
              msg.style.padding = "12px 24px";
              msg.style.borderRadius = "6px";
              msg.style.zIndex = "9999";
              msg.style.boxShadow = "0 2px 8px rgba(0,0,0,0.15)";
              msg.textContent =
                message ||
                "Service worker registration failed. Some features may be limited.";
              document.body.appendChild(msg);

              // Auto-hide after 5 seconds
              setTimeout(() => {
                hideServiceWorkerIssue();
              }, 5000);
            }
          }
          function hideServiceWorkerIssue() {
            const msg = document.getElementById("pwa-sw-issue-message");
            if (msg) msg.remove();
          }

          // Service worker registration function - works regardless of online status

          async function tryRegisterServiceWorker() {
            // Register service worker regardless of online status
            // Push notifications work offline via service worker
            try {
              // Check if the service worker file is accessible
              const response = await fetch(swUrl, {
                method: "HEAD",
                cache: "no-store",
              });
              if (!response.ok) {
                throw new Error(
                  "Service worker file not found at /firebase-messaging-sw.js. Please ensure it exists at the site root."
                );
              }
              // Proceed with registration
              const registration = await navigator.serviceWorker.register(
                swUrl,
                {
                  scope: "/",
                  type: "classic",
                  updateViaCache: "none",
                }
              );
              if (window.location.hostname === "localhost") {
                console.log(
                  "Service worker registration successful:",
                  registration
                );
              }
              // Periodically check for new service workers every hour
              setInterval(() => {
                registration.update();
              }, 60 * 60 * 1000);
              // Listen for controllerchange to reload the page
              navigator.serviceWorker.addEventListener(
                "controllerchange",
                () => {
                  console.log(
                    "New Service Worker activated, reloading page..."
                  );
                  window.location.reload();
                }
              );
              // Continue with messaging initialization regardless of online status
              try {
                await registration.update();
              } catch (err) {
                console.warn(
                  "Service worker update failed, but continuing:",
                  err
                );
              }
              if (window.location.hostname === "localhost") {
                console.log("Service Worker updated successfully");
              }
              window.qMessaging = messaging;
              messagingResolve(messaging);
            } catch (err) {
              console.error(
                "Failed to register service worker or initialize messaging:",
                err
              );
              // Show appropriate error message based on the actual error
              const errorMessage = err.message.includes(
                "Service worker file not found"
              )
                ? "Service worker file missing. Push notifications may not work properly."
                : "Failed to initialize push notifications. Please try refreshing the page.";

              showServiceWorkerIssue(errorMessage);
              messagingResolve(null);
            }
          }

          // Initial attempt
          tryRegisterServiceWorker();
        } else {
          console.error("Service workers are not supported in this browser");
          messagingResolve(null);
        }

        // Make messaging available globally
        window.qFirebase = {
          app,
          messaging,
        };
      } catch (error) {
        console.error("Failed to load Firebase modules:", error);
        messagingResolve(null);
      }
    };

    loadFirebase();
  } catch (error) {
    console.error("Failed to initialize Firebase:", error);
    messagingResolve(null);
  }
});

// Export the messaging promise for other modules to use
export { messagingPromise };
